/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Section Management */
.section {
    display: none;
    min-height: 100vh;
}

.section.active {
    display: block;
}

/* Upload Section Styles */
#uploadSection {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.upload-container {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
    text-align: center;
}

.upload-container h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5rem;
}

.subtitle {
    color: #7f8c8d;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.upload-area {
    border: 3px dashed #3498db;
    border-radius: 10px;
    padding: 60px 20px;
    margin: 30px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area:hover {
    border-color: #2980b9;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #27ae60;
    background: #e8f5e8;
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.upload-content h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.upload-content p {
    color: #7f8c8d;
    margin-bottom: 5px;
}

.file-types {
    font-size: 0.9rem;
    color: #95a5a6;
}

/* Progress Bar */
.upload-progress {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    width: 0%;
    transition: width 0.3s ease;
}

/* Extracted Questions */
.extracted-questions {
    margin-top: 30px;
    text-align: left;
}

.questions-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    background: #f8f9fa;
}

.question-preview {
    padding: 10px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}

.question-preview:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.question-preview h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.question-preview p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid #3498db;
    color: #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

/* Testing Interface */
#testingSection {
    background: #f8f9fa;
}

/* Menu Bar */
.menu-bar {
    background: white;
    padding: 15px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.progress-section {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.progress-current {
    color: #3498db;
    font-size: 1.2rem;
}

.jump-to-question {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.jump-to-question:hover {
    background: #ecf0f1;
}

.navigation-section {
    display: flex;
    gap: 15px;
}

.nav-btn {
    padding: 10px 20px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.nav-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

.timer-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.timer-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #e74c3c;
    font-family: 'Courier New', monospace;
}

.auto-advance-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.auto-btn {
    padding: 8px 12px;
    background: #95a5a6;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.auto-btn.active {
    background: #27ae60;
}

.auto-timer {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 600;
}

/* Question Container */
.question-container {
    max-width: 1000px;
    margin: 40px auto;
    padding: 0 30px;
}

.question-header {
    background: white;
    padding: 25px;
    border-radius: 10px 10px 0 0;
    border-bottom: 3px solid #3498db;
}

.question-title {
    color: #2c3e50;
    font-size: 1.5rem;
}

.question-content {
    background: white;
    padding: 30px 25px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.question-text {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #2c3e50;
    margin-bottom: 30px;
}

.answer-choices {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.answer-choice {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.answer-choice:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

.answer-choice.selected {
    border-color: #3498db;
    background: #e3f2fd;
}

.answer-choice.struck-out {
    opacity: 0.5;
    text-decoration: line-through;
    background: #ffebee;
    border-color: #e74c3c;
}

.answer-choice input[type="radio"] {
    margin-right: 12px;
    margin-top: 2px;
    transform: scale(1.2);
}

.answer-choice label {
    flex: 1;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1.5;
}

.question-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    min-width: 300px;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #2c3e50;
}

.modal-content input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ecf0f1;
    border-radius: 6px;
    font-size: 1rem;
    margin-bottom: 20px;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Loading Overlay */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.loading-overlay.active {
    display: flex;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #ecf0f1;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Review Interface */
#reviewSection {
    background: #f8f9fa;
    padding: 20px;
}

.review-header {
    background: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.review-header h1 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.review-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: bold;
}

.review-navigation {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.review-nav-btn {
    padding: 10px 20px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.review-nav-btn:hover {
    background: #2980b9;
}

.review-nav-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.review-progress {
    font-weight: 600;
    color: #2c3e50;
}

.review-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.review-question h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.review-question p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #2c3e50;
    margin-bottom: 25px;
}

.review-answers {
    margin-bottom: 25px;
}

.review-answer {
    padding: 12px 15px;
    margin-bottom: 10px;
    border-radius: 6px;
    border: 2px solid transparent;
}

.review-answer.correct-answer {
    background: #d5f4e6;
    border-color: #27ae60;
    color: #1e8449;
}

.review-answer.incorrect-answer {
    background: #fadbd8;
    border-color: #e74c3c;
    color: #c0392b;
}

.review-answer.user-correct {
    background: #d5f4e6;
    border-color: #27ae60;
    color: #1e8449;
    font-weight: bold;
}

.review-answer.struck-out {
    opacity: 0.6;
    text-decoration: line-through;
}

.review-explanation {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.review-explanation h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.review-explanation p {
    color: #5d6d7e;
    line-height: 1.6;
}

.review-actions {
    text-align: center;
}

/* Additional Styles */
.extraction-summary {
    background: #e8f5e8;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid #27ae60;
}

.choices-preview {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-top: 10px;
}

.choice-preview {
    font-size: 0.9rem;
    color: #7f8c8d;
    padding: 5px 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.more-questions {
    text-align: center;
    padding: 15px;
    color: #7f8c8d;
    font-style: italic;
}

.flagged {
    background: #f39c12 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-bar {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }
    
    .question-container {
        margin: 20px auto;
        padding: 0 15px;
    }
    
    .upload-container {
        margin: 20px;
        padding: 30px 20px;
    }
    
    .navigation-section {
        order: 3;
    }
    
    .timer-section {
        order: 1;
    }
    
    .progress-section {
        order: 2;
    }
    
    .review-stats {
        gap: 15px;
    }
    
    .review-navigation {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .stat-item {
        min-width: 80px;
    }
}
