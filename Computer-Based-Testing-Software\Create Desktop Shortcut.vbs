Set WshShell = CreateObject("WScript.Shell")
Set oShellLink = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\Computer-Based Testing Software.lnk")

' Get the current directory
strCurrentDir = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName)

' Set shortcut properties
oShellLink.TargetPath = strCurrentDir & "\Launch Testing Software.bat"
oShellLink.WorkingDirectory = strCurrentDir
oShellLink.Description = "Computer-Based Testing Software - Create tests from PDF, Word, and Image documents"
oShellLink.IconLocation = strCurrentDir & "\testing-app-icon.ico,0"

' Save the shortcut
oShellLink.Save

MsgBox "Desktop shortcut created successfully!" & vbCrLf & vbCrLf & "You can now launch the Computer-Based Testing Software from your desktop.", vbInformation, "Shortcut Created"
