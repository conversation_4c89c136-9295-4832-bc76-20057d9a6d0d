// Testing Engine Module
class TestingEngine {
    constructor() {
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.testStartTime = null;
        this.testDuration = 60 * 60 * 1000; // 60 minutes in milliseconds
        this.autoAdvanceEnabled = false;
        this.autoAdvanceInterval = 60 * 1000; // 1 minute per question
        this.autoAdvanceTimer = null;
        this.mainTimer = null;
        this.testCompleted = false;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Navigation buttons
        document.getElementById('prevBtn').addEventListener('click', () => this.previousQuestion());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextQuestion());
        
        // Jump to question
        document.getElementById('jumpToQuestion').addEventListener('click', () => this.showJumpModal());
        document.getElementById('jumpConfirm').addEventListener('click', () => this.jumpToQuestion());
        document.getElementById('jumpCancel').addEventListener('click', () => this.hideJumpModal());
        
        // Auto-advance toggle
        document.getElementById('autoAdvanceBtn').addEventListener('click', () => this.toggleAutoAdvance());
        
        // Question actions
        document.getElementById('clearAnswerBtn').addEventListener('click', () => this.clearAnswer());
        document.getElementById('flagQuestionBtn').addEventListener('click', () => this.toggleFlag());
        
        // Modal keyboard events
        document.getElementById('jumpInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.jumpToQuestion();
            if (e.key === 'Escape') this.hideJumpModal();
        });
    }

    loadQuestions(questions) {
        this.questions = questions.map((q, index) => ({
            ...q,
            number: index + 1 // Ensure sequential numbering
        }));
        this.currentQuestionIndex = 0;
        this.updateTotalQuestions();
    }

    startTest() {
        if (this.questions.length === 0) {
            alert('No questions loaded. Please upload a document first.');
            return;
        }

        this.testStartTime = Date.now();
        this.testCompleted = false;
        this.currentQuestionIndex = 0;
        
        // Reset all answers
        this.questions.forEach(q => {
            q.userAnswer = null;
            q.flagged = false;
            q.struckOut = [];
        });

        this.showTestingInterface();
        this.startMainTimer();
        this.displayCurrentQuestion();
        this.updateProgress();
    }

    showTestingInterface() {
        document.getElementById('uploadSection').classList.remove('active');
        document.getElementById('testingSection').classList.add('active');
        document.getElementById('reviewSection').classList.remove('active');
    }

    startMainTimer() {
        this.updateTimerDisplay();
        
        this.mainTimer = setInterval(() => {
            const elapsed = Date.now() - this.testStartTime;
            const remaining = this.testDuration - elapsed;
            
            if (remaining <= 0) {
                this.completeTest();
            } else {
                this.updateTimerDisplay();
            }
        }, 1000);
    }

    updateTimerDisplay() {
        const elapsed = Date.now() - this.testStartTime;
        const remaining = Math.max(0, this.testDuration - elapsed);
        
        const minutes = Math.floor(remaining / 60000);
        const seconds = Math.floor((remaining % 60000) / 1000);
        
        const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('timerDisplay').textContent = display;
        
        // Change color when time is running low
        const timerElement = document.getElementById('timerDisplay');
        if (remaining < 5 * 60 * 1000) { // Less than 5 minutes
            timerElement.style.color = '#e74c3c';
        } else if (remaining < 10 * 60 * 1000) { // Less than 10 minutes
            timerElement.style.color = '#f39c12';
        } else {
            timerElement.style.color = '#27ae60';
        }
    }

    displayCurrentQuestion() {
        const question = this.questions[this.currentQuestionIndex];
        if (!question) return;

        // Update question title and text
        document.getElementById('questionTitle').textContent = `Question ${question.number}`;
        document.getElementById('questionText').textContent = question.text;

        // Create answer choices
        const choicesContainer = document.getElementById('answerChoices');
        choicesContainer.innerHTML = '';

        question.choices.forEach((choice, index) => {
            const choiceElement = this.createChoiceElement(choice, index, question);
            choicesContainer.appendChild(choiceElement);
        });

        // Update flag button
        const flagBtn = document.getElementById('flagQuestionBtn');
        flagBtn.textContent = question.flagged ? '🚩 Unflag' : '🚩 Flag for Review';
        flagBtn.classList.toggle('flagged', question.flagged);

        this.updateProgress();
        this.updateNavigationButtons();
    }

    createChoiceElement(choice, index, question) {
        const choiceDiv = document.createElement('div');
        choiceDiv.className = 'answer-choice';
        
        if (question.userAnswer === choice.letter) {
            choiceDiv.classList.add('selected');
        }
        
        if (question.struckOut.includes(choice.letter)) {
            choiceDiv.classList.add('struck-out');
        }

        const radio = document.createElement('input');
        radio.type = 'radio';
        radio.name = `question_${question.id}`;
        radio.value = choice.letter;
        radio.id = `choice_${question.id}_${choice.letter}`;
        radio.checked = question.userAnswer === choice.letter;

        const label = document.createElement('label');
        label.htmlFor = radio.id;
        label.textContent = `${choice.letter.toUpperCase()}. ${choice.text}`;

        // Click event for selection
        choiceDiv.addEventListener('click', (e) => {
            if (e.target.type !== 'radio') {
                radio.checked = true;
            }
            this.selectAnswer(choice.letter);
        });

        // Right-click for strike-out
        choiceDiv.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.toggleStrikeOut(choice.letter);
        });

        choiceDiv.appendChild(radio);
        choiceDiv.appendChild(label);

        return choiceDiv;
    }

    selectAnswer(letter) {
        const question = this.questions[this.currentQuestionIndex];
        question.userAnswer = letter;
        this.displayCurrentQuestion(); // Refresh display
        this.saveProgress();
    }

    toggleStrikeOut(letter) {
        const question = this.questions[this.currentQuestionIndex];
        const index = question.struckOut.indexOf(letter);
        
        if (index > -1) {
            question.struckOut.splice(index, 1);
        } else {
            question.struckOut.push(letter);
        }
        
        this.displayCurrentQuestion(); // Refresh display
        this.saveProgress();
    }

    clearAnswer() {
        const question = this.questions[this.currentQuestionIndex];
        question.userAnswer = null;
        this.displayCurrentQuestion();
        this.saveProgress();
    }

    toggleFlag() {
        const question = this.questions[this.currentQuestionIndex];
        question.flagged = !question.flagged;
        this.displayCurrentQuestion();
        this.saveProgress();
    }

    previousQuestion() {
        if (this.currentQuestionIndex > 0) {
            this.currentQuestionIndex--;
            this.displayCurrentQuestion();
            this.resetAutoAdvanceTimer();
        }
    }

    nextQuestion() {
        if (this.currentQuestionIndex < this.questions.length - 1) {
            this.currentQuestionIndex++;
            this.displayCurrentQuestion();
            this.resetAutoAdvanceTimer();
        } else {
            // Last question - offer to complete test
            if (confirm('This is the last question. Would you like to complete the test?')) {
                this.completeTest();
            }
        }
    }

    showJumpModal() {
        const modal = document.getElementById('jumpModal');
        const input = document.getElementById('jumpInput');
        input.max = this.questions.length;
        input.value = this.currentQuestionIndex + 1;
        modal.classList.add('active');
        input.focus();
        input.select();
    }

    hideJumpModal() {
        document.getElementById('jumpModal').classList.remove('active');
    }

    jumpToQuestion() {
        const input = document.getElementById('jumpInput');
        const questionNumber = parseInt(input.value);
        
        if (questionNumber >= 1 && questionNumber <= this.questions.length) {
            this.currentQuestionIndex = questionNumber - 1;
            this.displayCurrentQuestion();
            this.hideJumpModal();
            this.resetAutoAdvanceTimer();
        } else {
            alert(`Please enter a number between 1 and ${this.questions.length}`);
        }
    }

    toggleAutoAdvance() {
        this.autoAdvanceEnabled = !this.autoAdvanceEnabled;
        const btn = document.getElementById('autoAdvanceBtn');
        
        if (this.autoAdvanceEnabled) {
            btn.textContent = '⏱️ Auto: ON';
            btn.classList.add('active');
            this.startAutoAdvanceTimer();
        } else {
            btn.textContent = '⏱️ Auto: OFF';
            btn.classList.remove('active');
            this.stopAutoAdvanceTimer();
        }
    }

    startAutoAdvanceTimer() {
        this.stopAutoAdvanceTimer(); // Clear any existing timer
        
        let timeLeft = this.autoAdvanceInterval / 1000; // Convert to seconds
        this.updateAutoTimer(timeLeft);
        
        this.autoAdvanceTimer = setInterval(() => {
            timeLeft--;
            this.updateAutoTimer(timeLeft);
            
            if (timeLeft <= 0) {
                this.nextQuestion();
                timeLeft = this.autoAdvanceInterval / 1000; // Reset timer
            }
        }, 1000);
    }

    stopAutoAdvanceTimer() {
        if (this.autoAdvanceTimer) {
            clearInterval(this.autoAdvanceTimer);
            this.autoAdvanceTimer = null;
        }
    }

    resetAutoAdvanceTimer() {
        if (this.autoAdvanceEnabled) {
            this.startAutoAdvanceTimer();
        }
    }

    updateAutoTimer(seconds) {
        document.getElementById('autoTimer').textContent = `${seconds}s`;
    }

    updateProgress() {
        document.getElementById('currentQuestion').textContent = this.currentQuestionIndex + 1;
    }

    updateTotalQuestions() {
        document.getElementById('totalQuestions').textContent = this.questions.length;
    }

    updateNavigationButtons() {
        document.getElementById('prevBtn').disabled = this.currentQuestionIndex === 0;
        document.getElementById('nextBtn').disabled = false; // Always allow next (or complete)
    }

    completeTest() {
        this.testCompleted = true;
        this.stopAutoAdvanceTimer();
        
        if (this.mainTimer) {
            clearInterval(this.mainTimer);
        }

        this.showReviewInterface();
    }

    showReviewInterface() {
        document.getElementById('testingSection').classList.remove('active');
        document.getElementById('reviewSection').classList.add('active');

        // Initialize review with first question
        this.currentQuestionIndex = 0;
        this.displayReviewQuestion();
        this.calculateAndDisplayStats();
    }

    displayReviewQuestion() {
        const question = this.questions[this.currentQuestionIndex];
        if (!question) return;

        // Update review progress
        document.getElementById('reviewProgress').textContent =
            `Question ${this.currentQuestionIndex + 1} of ${this.questions.length}`;

        // Display question
        const questionContainer = document.getElementById('reviewQuestion');
        questionContainer.innerHTML = `
            <h3>Question ${question.number}</h3>
            <p>${question.text}</p>
        `;

        // Display answers with correct/incorrect indicators
        const answersContainer = document.getElementById('reviewAnswers');
        answersContainer.innerHTML = '';

        question.choices.forEach(choice => {
            const answerDiv = document.createElement('div');
            answerDiv.className = 'review-answer';

            // Determine answer status
            const isUserAnswer = question.userAnswer === choice.letter;
            const isCorrectAnswer = question.correctAnswer === choice.letter;
            const isStruckOut = question.struckOut.includes(choice.letter);

            if (isCorrectAnswer) {
                answerDiv.classList.add('correct-answer');
            }
            if (isUserAnswer && !isCorrectAnswer) {
                answerDiv.classList.add('incorrect-answer');
            }
            if (isUserAnswer && isCorrectAnswer) {
                answerDiv.classList.add('user-correct');
            }
            if (isStruckOut) {
                answerDiv.classList.add('struck-out');
            }

            let indicator = '';
            if (isCorrectAnswer) {
                indicator = '✓ ';
            }
            if (isUserAnswer && !isCorrectAnswer) {
                indicator = '✗ ';
            }

            answerDiv.innerHTML = `
                ${indicator}<strong>${choice.letter.toUpperCase()}.</strong> ${choice.text}
                ${isUserAnswer ? ' <em>(Your answer)</em>' : ''}
            `;

            answersContainer.appendChild(answerDiv);
        });

        // Display explanation if available
        const explanationContainer = document.getElementById('reviewExplanation');
        if (question.explanation) {
            explanationContainer.innerHTML = `
                <h4>Explanation:</h4>
                <p>${question.explanation}</p>
            `;
            explanationContainer.style.display = 'block';
        } else {
            explanationContainer.style.display = 'none';
        }
    }

    calculateAndDisplayStats() {
        const totalQuestions = this.questions.length;
        const answeredQuestions = this.questions.filter(q => q.userAnswer !== null).length;
        const correctAnswers = this.questions.filter(q =>
            q.userAnswer === q.correctAnswer && q.correctAnswer !== null
        ).length;
        const flaggedQuestions = this.questions.filter(q => q.flagged).length;

        const percentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;

        document.getElementById('reviewStats').innerHTML = `
            <div class="stat-item">
                <span class="stat-label">Total Questions:</span>
                <span class="stat-value">${totalQuestions}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Answered:</span>
                <span class="stat-value">${answeredQuestions}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Correct:</span>
                <span class="stat-value">${correctAnswers}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Score:</span>
                <span class="stat-value">${percentage}%</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Flagged:</span>
                <span class="stat-value">${flaggedQuestions}</span>
            </div>
        `;
    }

    saveProgress() {
        const progressData = {
            questions: this.questions,
            currentQuestionIndex: this.currentQuestionIndex,
            testStartTime: this.testStartTime,
            autoAdvanceEnabled: this.autoAdvanceEnabled
        };
        
        localStorage.setItem('testProgress', JSON.stringify(progressData));
    }

    loadProgress() {
        const saved = localStorage.getItem('testProgress');
        if (saved) {
            const data = JSON.parse(saved);
            this.questions = data.questions || [];
            this.currentQuestionIndex = data.currentQuestionIndex || 0;
            this.testStartTime = data.testStartTime;
            this.autoAdvanceEnabled = data.autoAdvanceEnabled || false;
            return true;
        }
        return false;
    }
}

// Export for use in other modules
window.TestingEngine = TestingEngine;
