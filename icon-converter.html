<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #2980b9;
        }
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon-preview img {
            display: block;
            margin: 0 auto 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Testing Software Icon Converter</h1>
        <p>This tool converts the SVG icon to different formats for use as application icons.</p>
        
        <canvas id="iconCanvas" width="128" height="128"></canvas>
        
        <div>
            <button onclick="generateIcon(32)">Generate 32x32 Icon</button>
            <button onclick="generateIcon(64)">Generate 64x64 Icon</button>
            <button onclick="generateIcon(128)">Generate 128x128 Icon</button>
            <button onclick="generateIcon(256)">Generate 256x256 Icon</button>
        </div>
        
        <div id="iconPreviews"></div>
        
        <p><strong>Instructions:</strong></p>
        <ol style="text-align: left;">
            <li>Click any of the "Generate" buttons above</li>
            <li>Right-click on the generated icon and select "Save image as..."</li>
            <li>Save as "testing-app-icon.png" or "testing-app-icon.ico"</li>
            <li>Use this icon file for your desktop shortcut</li>
        </ol>
    </div>

    <script>
        const svgIcon = `
        <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
            </linearGradient>
            <linearGradient id="paperGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
            </linearGradient>
            <linearGradient id="timerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
            </linearGradient>
            <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
              <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
            </filter>
          </defs>
          
          <circle cx="64" cy="64" r="60" fill="url(#backgroundGradient)" filter="url(#shadow)"/>
          <rect x="25" y="20" width="50" height="65" rx="3" ry="3" fill="url(#paperGradient)" stroke="#e0e0e0" stroke-width="1"/>
          <line x1="30" y1="30" x2="65" y2="30" stroke="#3498db" stroke-width="2" stroke-linecap="round"/>
          <line x1="30" y1="37" x2="60" y2="37" stroke="#95a5a6" stroke-width="1.5" stroke-linecap="round"/>
          <line x1="30" y1="44" x2="62" y2="44" stroke="#95a5a6" stroke-width="1.5" stroke-linecap="round"/>
          <circle cx="32" cy="52" r="3" fill="none" stroke="#3498db" stroke-width="1.5"/>
          <text x="38" y="55" font-family="Arial, sans-serif" font-size="8" fill="#2c3e50">A</text>
          <circle cx="32" cy="60" r="3" fill="#3498db" stroke="#3498db" stroke-width="1.5"/>
          <text x="38" y="63" font-family="Arial, sans-serif" font-size="8" fill="#2c3e50">B</text>
          <circle cx="32" cy="68" r="3" fill="none" stroke="#3498db" stroke-width="1.5"/>
          <text x="38" y="71" font-family="Arial, sans-serif" font-size="8" fill="#2c3e50">C</text>
          <circle cx="32" cy="76" r="3" fill="none" stroke="#3498db" stroke-width="1.5"/>
          <text x="38" y="79" font-family="Arial, sans-serif" font-size="8" fill="#2c3e50">D</text>
          <circle cx="85" cy="35" r="12" fill="url(#timerGradient)" stroke="#ffffff" stroke-width="2"/>
          <circle cx="85" cy="35" r="8" fill="none" stroke="#ffffff" stroke-width="1.5"/>
          <line x1="85" y1="35" x2="85" y2="30" stroke="#ffffff" stroke-width="2" stroke-linecap="round"/>
          <line x1="85" y1="35" x2="89" y2="35" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round"/>
          <rect x="25" y="95" width="50" height="8" rx="4" ry="4" fill="#ecf0f1"/>
          <rect x="25" y="95" width="20" height="8" rx="4" ry="4" fill="#3498db"/>
          <text x="85" y="100" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#ffffff" text-anchor="middle">Q</text>
          <text x="85" y="112" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">1/50</text>
          <path d="M 45 15 L 50 10 L 55 15 M 50 10 L 50 20" stroke="#3498db" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" opacity="0.7"/>
        </svg>`;

        function generateIcon(size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;

            const img = new Image();
            const svgBlob = new Blob([svgIcon], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);

            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                // Create preview
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                
                const iconImg = document.createElement('img');
                iconImg.src = canvas.toDataURL('image/png');
                iconImg.width = size > 64 ? 64 : size;
                iconImg.height = size > 64 ? 64 : size;
                iconImg.title = `Right-click to save ${size}x${size} icon`;
                
                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                
                preview.appendChild(iconImg);
                preview.appendChild(label);
                
                document.getElementById('iconPreviews').appendChild(preview);
                
                URL.revokeObjectURL(url);
            };

            img.src = url;
        }

        // Generate default 128x128 icon on load
        window.onload = function() {
            generateIcon(128);
        };
    </script>
</body>
</html>
