<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Computer-Based Testing Software</title>
    <link rel="icon" type="image/svg+xml" href="testing-app-icon.svg">
    <link rel="stylesheet" href="testing-styles.css">
    <!-- PDF.js for PDF processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <!-- Mammoth.js for Word document processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>
    <!-- Tesseract.js for OCR on images -->
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>
</head>
<body>
    <!-- Upload Interface -->
    <div id="uploadSection" class="section active">
        <div class="upload-container">
            <h1>Computer-Based Testing Software</h1>
            <p class="subtitle">Upload your document to generate questions</p>
            
            <div class="upload-area" id="uploadArea">
                <div class="upload-content">
                    <div class="upload-icon">📄</div>
                    <h3>Drag & Drop your document here</h3>
                    <p>or click to browse</p>
                    <p class="file-types">Supported: PDF, Word (.docx), JPEG, PNG</p>
                </div>
                <input type="file" id="fileInput" accept=".pdf,.docx,.jpg,.jpeg,.png" hidden>
            </div>
            
            <div class="upload-progress" id="uploadProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">Processing document...</p>
            </div>
            
            <div class="extracted-questions" id="extractedQuestions" style="display: none;">
                <h3>Extracted Questions Preview</h3>
                <div class="questions-list" id="questionsList"></div>
                <div class="start-test-controls">
                    <button id="startTestBtn" class="btn btn-primary">Start Test (50 Questions, 60 Minutes)</button>
                    <button id="editQuestionsBtn" class="btn btn-secondary">Edit Questions</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Testing Interface -->
    <div id="testingSection" class="section">
        <!-- Menu Bar -->
        <div class="menu-bar">
            <div class="progress-section">
                <span class="progress-label">Question:</span>
                <span class="progress-current" id="currentQuestion">1</span>
                <span class="progress-separator">/</span>
                <span class="progress-total" id="totalQuestions">50</span>
                <button class="jump-to-question" id="jumpToQuestion" title="Jump to question">🔍</button>
            </div>
            
            <div class="navigation-section">
                <button class="nav-btn" id="prevBtn" title="Previous Question">← Previous</button>
                <button class="nav-btn" id="nextBtn" title="Next Question">Next →</button>
            </div>
            
            <div class="timer-section">
                <div class="timer-display" id="timerDisplay">60:00</div>
                <div class="auto-advance-controls">
                    <button class="auto-btn" id="autoAdvanceBtn" title="Toggle Auto-Advance">⏱️ Auto: OFF</button>
                    <span class="auto-timer" id="autoTimer">60s</span>
                </div>
            </div>
        </div>

        <!-- Question Content -->
        <div class="question-container">
            <div class="question-header">
                <h2 class="question-title" id="questionTitle">Question 1</h2>
            </div>
            
            <div class="question-content">
                <div class="question-text" id="questionText">
                    <!-- Question content will be loaded here -->
                </div>
                
                <div class="answer-choices" id="answerChoices">
                    <!-- Answer choices will be loaded here -->
                </div>
            </div>
            
            <div class="question-actions">
                <button class="btn btn-outline" id="clearAnswerBtn">Clear Answer</button>
                <button class="btn btn-outline" id="flagQuestionBtn">🚩 Flag for Review</button>
            </div>
        </div>
    </div>

    <!-- Review Interface -->
    <div id="reviewSection" class="section">
        <div class="review-header">
            <h1>Test Review</h1>
            <div class="review-stats" id="reviewStats">
                <!-- Test statistics will be shown here -->
            </div>
        </div>
        
        <div class="review-navigation">
            <button class="review-nav-btn" id="reviewPrevBtn">← Previous</button>
            <span class="review-progress" id="reviewProgress">Question 1 of 50</span>
            <button class="review-nav-btn" id="reviewNextBtn">Next →</button>
        </div>
        
        <div class="review-content">
            <div class="review-question" id="reviewQuestion">
                <!-- Review question content -->
            </div>
            
            <div class="review-answers" id="reviewAnswers">
                <!-- Review answers with correct/incorrect indicators -->
            </div>
            
            <div class="review-explanation" id="reviewExplanation">
                <!-- Explanation if available -->
            </div>
        </div>
        
        <div class="review-actions">
            <button class="btn btn-primary" id="retakeTestBtn">Retake Test</button>
            <button class="btn btn-secondary" id="newTestBtn">Upload New Document</button>
        </div>
    </div>

    <!-- Jump to Question Modal -->
    <div id="jumpModal" class="modal">
        <div class="modal-content">
            <h3>Jump to Question</h3>
            <input type="number" id="jumpInput" min="1" max="50" placeholder="Enter question number">
            <div class="modal-actions">
                <button class="btn btn-primary" id="jumpConfirm">Go</button>
                <button class="btn btn-secondary" id="jumpCancel">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p id="loadingText">Processing document...</p>
        </div>
    </div>

    <script src="document-processor.js"></script>
    <script src="question-extractor.js"></script>
    <script src="testing-engine.js"></script>
    <script src="app.js"></script>
</body>
</html>
