// Tetris Game Implementation
class TetrisGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextCanvas');
        this.nextCtx = this.nextCanvas.getContext('2d');
        
        // Game dimensions
        this.BOARD_WIDTH = 10;
        this.BOARD_HEIGHT = 20;
        this.BLOCK_SIZE = 30;
        
        // Game state
        this.board = [];
        this.currentPiece = null;
        this.nextPiece = null;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameRunning = false;
        this.gamePaused = false;
        this.dropTime = 0;
        this.dropInterval = 1000; // milliseconds

        // Particle system
        this.particles = [];

        // Initialize board
        this.initBoard();
        this.initEventListeners();
        this.generateNextPiece();
    }
    
    initBoard() {
        this.board = Array(this.BOARD_HEIGHT).fill().map(() => Array(this.BOARD_WIDTH).fill(0));
    }
    
    initEventListeners() {
        // Button events
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('restartBtn').addEventListener('click', () => this.restartGame());
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
    }
    
    // Tetris pieces (tetrominoes)
    getPieces() {
        return {
            I: {
                shape: [
                    [1, 1, 1, 1]
                ],
                color: '#00f5ff'
            },
            O: {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffff00'
            },
            T: {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1]
                ],
                color: '#800080'
            },
            S: {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0]
                ],
                color: '#00ff00'
            },
            Z: {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1]
                ],
                color: '#ff0000'
            },
            J: {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1]
                ],
                color: '#0000ff'
            },
            L: {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1]
                ],
                color: '#ffa500'
            }
        };
    }
    
    generateNextPiece() {
        const pieces = Object.keys(this.getPieces());
        const randomPiece = pieces[Math.floor(Math.random() * pieces.length)];
        const pieceData = this.getPieces()[randomPiece];
        
        this.nextPiece = {
            type: randomPiece,
            shape: pieceData.shape,
            color: pieceData.color,
            x: Math.floor(this.BOARD_WIDTH / 2) - Math.floor(pieceData.shape[0].length / 2),
            y: 0
        };
    }
    
    spawnPiece() {
        if (this.nextPiece) {
            this.currentPiece = { ...this.nextPiece };
            this.generateNextPiece();
            
            // Check for game over
            if (this.checkCollision(this.currentPiece, 0, 0)) {
                this.gameOver();
                return false;
            }
        }
        return true;
    }
    
    checkCollision(piece, dx, dy) {
        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const newX = piece.x + x + dx;
                    const newY = piece.y + y + dy;
                    
                    if (newX < 0 || newX >= this.BOARD_WIDTH || 
                        newY >= this.BOARD_HEIGHT ||
                        (newY >= 0 && this.board[newY][newX])) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    movePiece(dx, dy) {
        if (this.currentPiece && !this.checkCollision(this.currentPiece, dx, dy)) {
            this.currentPiece.x += dx;
            this.currentPiece.y += dy;
            return true;
        }
        return false;
    }
    
    rotatePiece() {
        if (!this.currentPiece) return;
        
        const rotated = this.currentPiece.shape[0].map((_, index) =>
            this.currentPiece.shape.map(row => row[index]).reverse()
        );
        
        const originalShape = this.currentPiece.shape;
        this.currentPiece.shape = rotated;
        
        if (this.checkCollision(this.currentPiece, 0, 0)) {
            this.currentPiece.shape = originalShape;
        }
    }
    
    placePiece() {
        if (!this.currentPiece) return;
        
        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    const boardY = this.currentPiece.y + y;
                    const boardX = this.currentPiece.x + x;
                    if (boardY >= 0) {
                        this.board[boardY][boardX] = this.currentPiece.color;
                    }
                }
            }
        }
        
        this.clearLines();
        this.currentPiece = null;
    }
    
    clearLines() {
        let linesCleared = 0;
        let clearedRows = [];

        for (let y = this.BOARD_HEIGHT - 1; y >= 0; y--) {
            if (this.board[y].every(cell => cell !== 0)) {
                clearedRows.push(y);
                linesCleared++;
            }
        }

        if (linesCleared > 0) {
            // Flash effect for cleared lines
            this.flashClearedLines(clearedRows);

            // Remove cleared lines after flash
            setTimeout(() => {
                clearedRows.forEach(() => {
                    for (let y = this.BOARD_HEIGHT - 1; y >= 0; y--) {
                        if (this.board[y].every(cell => cell !== 0)) {
                            this.board.splice(y, 1);
                            this.board.unshift(Array(this.BOARD_WIDTH).fill(0));
                            break;
                        }
                    }
                });

                this.lines += linesCleared;
                this.score += this.calculateScore(linesCleared);
                this.level = Math.floor(this.lines / 10) + 1;
                this.dropInterval = Math.max(50, 1000 - (this.level - 1) * 50);
                this.updateDisplay();
                this.draw();
            }, 300);
        }
    }

    flashClearedLines(rows) {
        let flashCount = 0;
        const maxFlashes = 6;

        // Create particles for cleared lines
        rows.forEach(row => {
            for (let x = 0; x < this.BOARD_WIDTH; x++) {
                const color = this.board[row][x];
                if (color && color !== '#ffffff' && color !== '#000000') {
                    this.createParticles(
                        x * this.BLOCK_SIZE,
                        row * this.BLOCK_SIZE,
                        color
                    );
                }
            }
        });

        const flash = () => {
            // Toggle visibility of cleared lines
            rows.forEach(row => {
                for (let x = 0; x < this.BOARD_WIDTH; x++) {
                    if (flashCount % 2 === 0) {
                        this.board[row][x] = '#ffffff';
                    } else {
                        this.board[row][x] = '#000000';
                    }
                }
            });

            this.draw();
            flashCount++;

            if (flashCount < maxFlashes) {
                setTimeout(flash, 50);
            }
        };

        flash();
    }
    
    calculateScore(linesCleared) {
        const baseScore = [0, 40, 100, 300, 1200];
        return baseScore[linesCleared] * this.level;
    }
    
    hardDrop() {
        if (!this.currentPiece) return;

        while (this.movePiece(0, 1)) {
            // Keep dropping until collision
        }
        this.placePiece();
    }

    handleKeyPress(e) {
        if (!this.gameRunning || this.gamePaused) return;

        switch(e.code) {
            case 'ArrowLeft':
                e.preventDefault();
                this.movePiece(-1, 0);
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.movePiece(1, 0);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.movePiece(0, 1);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.rotatePiece();
                break;
            case 'Space':
                e.preventDefault();
                this.hardDrop();
                break;
            case 'KeyP':
                e.preventDefault();
                this.togglePause();
                break;
        }
        this.draw();
    }

    startGame() {
        this.gameRunning = true;
        this.gamePaused = false;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.dropInterval = 1000;
        this.initBoard();
        this.generateNextPiece();
        this.spawnPiece();
        this.updateDisplay();
        this.hideGameOver();

        document.getElementById('startBtn').disabled = true;
        document.getElementById('pauseBtn').disabled = false;

        this.gameLoop();
    }

    togglePause() {
        if (!this.gameRunning) return;

        this.gamePaused = !this.gamePaused;
        document.getElementById('pauseBtn').textContent = this.gamePaused ? 'Resume' : 'Pause';

        if (!this.gamePaused) {
            this.gameLoop();
        }
    }

    gameOver() {
        this.gameRunning = false;
        this.gamePaused = false;

        document.getElementById('startBtn').disabled = false;
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('pauseBtn').textContent = 'Pause';

        this.showGameOver();
    }

    restartGame() {
        this.hideGameOver();
        this.startGame();
    }

    showGameOver() {
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('gameOverScreen').classList.remove('hidden');
    }

    hideGameOver() {
        document.getElementById('gameOverScreen').classList.add('hidden');
    }

    updateDisplay() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('level').textContent = this.level;
        document.getElementById('lines').textContent = this.lines;
    }

    draw() {
        // Clear main canvas
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw board
        this.drawBoard();

        // Draw ghost piece (preview)
        if (this.currentPiece) {
            this.drawGhostPiece();
        }

        // Draw current piece
        if (this.currentPiece) {
            this.drawPiece(this.currentPiece, this.ctx);
        }

        // Draw next piece
        this.drawNextPiece();

        // Update and draw particles
        this.updateParticles();
        this.drawParticles();
    }

    drawGhostPiece() {
        if (!this.currentPiece) return;

        // Find the lowest position for the current piece
        let ghostY = this.currentPiece.y;
        while (!this.checkCollision({...this.currentPiece, y: ghostY + 1}, 0, 0)) {
            ghostY++;
        }

        // Draw ghost piece with transparency
        this.ctx.save();
        this.ctx.globalAlpha = 0.3;
        this.ctx.fillStyle = this.currentPiece.color;

        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    const drawX = (this.currentPiece.x + x) * this.BLOCK_SIZE;
                    const drawY = (ghostY + y) * this.BLOCK_SIZE;

                    this.ctx.fillRect(drawX, drawY, this.BLOCK_SIZE, this.BLOCK_SIZE);

                    // Add dashed border
                    this.ctx.strokeStyle = this.currentPiece.color;
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([5, 5]);
                    this.ctx.strokeRect(drawX, drawY, this.BLOCK_SIZE, this.BLOCK_SIZE);
                }
            }
        }

        this.ctx.restore();
        this.ctx.setLineDash([]);
    }

    createParticles(x, y, color) {
        for (let i = 0; i < 8; i++) {
            this.particles.push({
                x: x + Math.random() * this.BLOCK_SIZE,
                y: y + Math.random() * this.BLOCK_SIZE,
                vx: (Math.random() - 0.5) * 4,
                vy: (Math.random() - 0.5) * 4,
                color: color,
                life: 1.0,
                decay: 0.02
            });
        }
    }

    updateParticles() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay;

            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }

    drawParticles() {
        this.ctx.save();
        for (const particle of this.particles) {
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.fillRect(particle.x, particle.y, 3, 3);
        }
        this.ctx.restore();
    }

    drawBoard() {
        for (let y = 0; y < this.BOARD_HEIGHT; y++) {
            for (let x = 0; x < this.BOARD_WIDTH; x++) {
                if (this.board[y][x]) {
                    this.ctx.fillStyle = this.board[y][x];
                    this.ctx.fillRect(
                        x * this.BLOCK_SIZE,
                        y * this.BLOCK_SIZE,
                        this.BLOCK_SIZE,
                        this.BLOCK_SIZE
                    );

                    // Add border
                    this.ctx.strokeStyle = '#333';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(
                        x * this.BLOCK_SIZE,
                        y * this.BLOCK_SIZE,
                        this.BLOCK_SIZE,
                        this.BLOCK_SIZE
                    );
                }
            }
        }
    }

    drawPiece(piece, context) {
        context.fillStyle = piece.color;

        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const drawX = (piece.x + x) * this.BLOCK_SIZE;
                    const drawY = (piece.y + y) * this.BLOCK_SIZE;

                    context.fillRect(drawX, drawY, this.BLOCK_SIZE, this.BLOCK_SIZE);

                    // Add border
                    context.strokeStyle = '#333';
                    context.lineWidth = 1;
                    context.strokeRect(drawX, drawY, this.BLOCK_SIZE, this.BLOCK_SIZE);
                }
            }
        }
    }

    drawNextPiece() {
        // Clear next canvas
        this.nextCtx.fillStyle = '#000';
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);

        if (this.nextPiece) {
            const centerX = (this.nextCanvas.width - this.nextPiece.shape[0].length * 20) / 2;
            const centerY = (this.nextCanvas.height - this.nextPiece.shape.length * 20) / 2;

            this.nextCtx.fillStyle = this.nextPiece.color;

            for (let y = 0; y < this.nextPiece.shape.length; y++) {
                for (let x = 0; x < this.nextPiece.shape[y].length; x++) {
                    if (this.nextPiece.shape[y][x]) {
                        this.nextCtx.fillRect(
                            centerX + x * 20,
                            centerY + y * 20,
                            20,
                            20
                        );

                        // Add border
                        this.nextCtx.strokeStyle = '#333';
                        this.nextCtx.lineWidth = 1;
                        this.nextCtx.strokeRect(
                            centerX + x * 20,
                            centerY + y * 20,
                            20,
                            20
                        );
                    }
                }
            }
        }
    }

    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;

        const currentTime = Date.now();

        if (currentTime - this.dropTime > this.dropInterval) {
            if (this.currentPiece) {
                if (!this.movePiece(0, 1)) {
                    this.placePiece();
                    if (!this.spawnPiece()) {
                        return; // Game over
                    }
                }
            } else {
                if (!this.spawnPiece()) {
                    return; // Game over
                }
            }
            this.dropTime = currentTime;
        }

        this.draw();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new TetrisGame();
    game.draw();
});
