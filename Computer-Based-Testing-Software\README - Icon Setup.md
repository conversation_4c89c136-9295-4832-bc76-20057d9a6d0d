# Computer-Based Testing Software - Icon Setup Guide

## 📋 Overview
This guide will help you set up a desktop icon for your Computer-Based Testing Software.

## 🎨 Icon Files Included
- `testing-app-icon.svg` - Vector icon (scalable)
- `icon-converter.html` - Tool to convert SVG to PNG/ICO formats
- `Launch Testing Software.bat` - Windows launcher script
- `Create Desktop Shortcut.vbs` - Automatic shortcut creator

## 🚀 Quick Setup (Windows)

### Method 1: Automatic Shortcut Creation
1. **Double-click** `Create Desktop Shortcut.vbs`
2. Click "OK" when prompted
3. A shortcut will appear on your desktop
4. Double-click the desktop shortcut to launch the testing software

### Method 2: Manual Setup
1. **Create Icon File:**
   - Open `icon-converter.html` in your browser
   - Click "Generate 128x128 Icon"
   - Right-click the generated icon and "Save image as..."
   - Save as `testing-app-icon.png` in the same folder

2. **Create Desktop Shortcut:**
   - Right-click on your desktop
   - Select "New" → "Shortcut"
   - Browse to and select `Launch Testing Software.bat`
   - Name it "Computer-Based Testing Software"
   - Right-click the shortcut → "Properties"
   - Click "Change Icon" and browse to `testing-app-icon.png`
   - Click "OK" to save

## 🖥️ For Other Operating Systems

### macOS
1. Open `icon-converter.html` and generate a 512x512 icon
2. Save as `testing-app-icon.png`
3. Create an Automator application:
   - Open Automator
   - Choose "Application"
   - Add "Run Shell Script" action
   - Enter: `open "$(dirname "$0")/testing-app.html"`
   - Save as "Testing Software.app"
   - Right-click the app → "Get Info"
   - Drag the icon file to the icon area

### Linux
1. Generate icon using `icon-converter.html`
2. Create a `.desktop` file:
```ini
[Desktop Entry]
Version=1.0
Type=Application
Name=Computer-Based Testing Software
Comment=Create tests from PDF, Word, and Image documents
Exec=/path/to/your/folder/Launch Testing Software.bat
Icon=/path/to/your/folder/testing-app-icon.png
Terminal=false
Categories=Education;Office;
```

## 🎯 Icon Features
The custom icon includes:
- **Document representation** - Shows a test paper with questions
- **Multiple choice indicators** - Radio buttons A, B, C, D
- **Timer symbol** - Represents the 60-minute time limit
- **Progress bar** - Shows test progression
- **Question counter** - Displays "Q 1/50"
- **Upload arrow** - Indicates document upload capability

## 📁 File Organization
Keep all files in the same folder:
```
📁 Computer-Based Testing Software/
├── 📄 testing-app.html (main application)
├── 📄 testing-styles.css
├── 📄 document-processor.js
├── 📄 question-extractor.js
├── 📄 testing-engine.js
├── 📄 app.js
├── 🎨 testing-app-icon.svg
├── 🚀 Launch Testing Software.bat
├── ⚙️ Create Desktop Shortcut.vbs
├── 🔧 icon-converter.html
└── 📖 README - Icon Setup.md
```

## 🔧 Troubleshooting

### Icon Not Showing
- Make sure the icon file is in the same folder as the launcher
- Try using PNG format instead of ICO
- Refresh the desktop or restart Windows Explorer

### Shortcut Not Working
- Verify all files are in the same folder
- Check that the batch file path is correct
- Run as administrator if needed

### Browser Not Opening
- Set your default browser in Windows Settings
- Try manually opening `testing-app.html` in your browser
- Check if antivirus is blocking the batch file

## 📞 Support
If you encounter any issues:
1. Ensure all files are in the same directory
2. Check that your browser supports HTML5 and JavaScript
3. Verify file permissions allow execution
4. Try running the batch file directly first

## ✨ Features Reminder
Your testing software includes:
- ✅ PDF, Word, and Image document processing
- ✅ 50 questions per test block
- ✅ 60-minute timer with auto-advance
- ✅ Single-answer selection with strike-out
- ✅ Progress tracking and navigation
- ✅ Post-test review with explanations
- ✅ **550MB file upload limit** (updated!)

Enjoy your professional testing software! 🎓
