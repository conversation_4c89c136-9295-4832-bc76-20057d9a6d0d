// Main Application Controller
class TestingApp {
    constructor() {
        this.documentProcessor = new DocumentProcessor();
        this.questionExtractor = new QuestionExtractor();
        this.testingEngine = new TestingEngine();
        this.currentQuestions = [];
        
        this.initializeApp();
    }

    initializeApp() {
        this.setupUploadInterface();
        this.setupReviewInterface();
        this.checkForSavedProgress();
    }

    setupUploadInterface() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const startTestBtn = document.getElementById('startTestBtn');
        const editQuestionsBtn = document.getElementById('editQuestionsBtn');

        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileUpload(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileUpload(e.target.files[0]);
            }
        });

        // Start test button
        startTestBtn.addEventListener('click', () => {
            if (this.currentQuestions.length > 0) {
                this.startTest();
            } else {
                alert('No questions available. Please upload a document first.');
            }
        });

        // Edit questions button
        editQuestionsBtn.addEventListener('click', () => {
            this.showQuestionEditor();
        });
    }

    setupReviewInterface() {
        // Review navigation
        document.getElementById('reviewPrevBtn').addEventListener('click', () => {
            if (this.testingEngine.currentQuestionIndex > 0) {
                this.testingEngine.currentQuestionIndex--;
                this.testingEngine.displayReviewQuestion();
            }
        });

        document.getElementById('reviewNextBtn').addEventListener('click', () => {
            if (this.testingEngine.currentQuestionIndex < this.testingEngine.questions.length - 1) {
                this.testingEngine.currentQuestionIndex++;
                this.testingEngine.displayReviewQuestion();
            }
        });

        // Review actions
        document.getElementById('retakeTestBtn').addEventListener('click', () => {
            this.retakeTest();
        });

        document.getElementById('newTestBtn').addEventListener('click', () => {
            this.newTest();
        });
    }

    async handleFileUpload(file) {
        try {
            // Validate file
            const validation = this.documentProcessor.validateFile(file);
            if (!validation.valid) {
                alert(validation.error);
                return;
            }

            // Show loading
            this.showLoading('Processing document...');

            // Get file info
            const fileInfo = this.documentProcessor.getFileInfo(file);
            console.log('Processing file:', fileInfo);

            // Process document
            const documentData = await this.documentProcessor.processFile(file);
            console.log('Document processed:', documentData);

            // Update loading message
            this.updateLoadingMessage('Extracting questions...');

            // Extract questions
            const extractionResult = this.questionExtractor.extractQuestions(documentData.text);
            
            if (extractionResult.success && extractionResult.questions.length > 0) {
                this.currentQuestions = extractionResult.questions;
                this.displayExtractedQuestions(extractionResult);
            } else {
                // Fallback to sample questions if extraction fails
                console.warn('Question extraction failed, using sample questions');
                this.currentQuestions = this.questionExtractor.generateSampleQuestions();
                this.displaySampleQuestions();
            }

            this.hideLoading();

        } catch (error) {
            console.error('Error processing file:', error);
            this.hideLoading();
            alert(`Error processing file: ${error.message}`);
        }
    }

    displayExtractedQuestions(result) {
        const container = document.getElementById('extractedQuestions');
        const listContainer = document.getElementById('questionsList');
        
        // Clear previous content
        listContainer.innerHTML = '';

        // Show summary
        const summary = document.createElement('div');
        summary.className = 'extraction-summary';
        summary.innerHTML = `
            <p><strong>Successfully extracted ${result.questions.length} questions</strong></p>
            ${result.totalFound > 50 ? `<p><em>Note: Found ${result.totalFound} questions total, showing first 50</em></p>` : ''}
        `;
        listContainer.appendChild(summary);

        // Show first few questions as preview
        const previewCount = Math.min(5, result.questions.length);
        for (let i = 0; i < previewCount; i++) {
            const question = result.questions[i];
            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-preview';
            questionDiv.innerHTML = `
                <h4>Question ${question.number}</h4>
                <p>${question.text}</p>
                <div class="choices-preview">
                    ${question.choices.map(choice => 
                        `<span class="choice-preview">${choice.letter.toUpperCase()}. ${choice.text.substring(0, 50)}${choice.text.length > 50 ? '...' : ''}</span>`
                    ).join('')}
                </div>
            `;
            listContainer.appendChild(questionDiv);
        }

        if (result.questions.length > previewCount) {
            const moreDiv = document.createElement('div');
            moreDiv.className = 'more-questions';
            moreDiv.innerHTML = `<p><em>... and ${result.questions.length - previewCount} more questions</em></p>`;
            listContainer.appendChild(moreDiv);
        }

        container.style.display = 'block';
    }

    displaySampleQuestions() {
        const container = document.getElementById('extractedQuestions');
        const listContainer = document.getElementById('questionsList');
        
        listContainer.innerHTML = `
            <div class="extraction-summary">
                <p><strong>Using sample questions for demonstration</strong></p>
                <p><em>The document processing didn't find recognizable questions, so we've generated 50 sample questions for you to test the interface.</em></p>
            </div>
            <div class="question-preview">
                <h4>Sample Question Format</h4>
                <p>Each question will have multiple choice answers that you can select, strike out, and navigate through.</p>
            </div>
        `;

        container.style.display = 'block';
    }

    startTest() {
        if (this.currentQuestions.length === 0) {
            alert('No questions available to start the test.');
            return;
        }

        // Load questions into testing engine
        this.testingEngine.loadQuestions(this.currentQuestions);
        
        // Start the test
        this.testingEngine.startTest();
        
        // Clear any saved progress since we're starting fresh
        localStorage.removeItem('testProgress');
    }

    retakeTest() {
        if (this.currentQuestions.length > 0) {
            this.startTest();
        } else {
            alert('No questions available. Please upload a document first.');
        }
    }

    newTest() {
        // Clear current data
        this.currentQuestions = [];
        localStorage.removeItem('testProgress');
        
        // Reset file input
        document.getElementById('fileInput').value = '';
        
        // Hide extracted questions
        document.getElementById('extractedQuestions').style.display = 'none';
        
        // Show upload section
        document.getElementById('uploadSection').classList.add('active');
        document.getElementById('testingSection').classList.remove('active');
        document.getElementById('reviewSection').classList.remove('active');
    }

    showQuestionEditor() {
        // This could be expanded to allow editing of extracted questions
        alert('Question editing feature coming soon! For now, you can start the test with the extracted questions.');
    }

    checkForSavedProgress() {
        if (this.testingEngine.loadProgress()) {
            const resume = confirm('Found saved test progress. Would you like to resume your previous test?');
            if (resume) {
                this.testingEngine.showTestingInterface();
                this.testingEngine.displayCurrentQuestion();
                this.testingEngine.startMainTimer();
            } else {
                localStorage.removeItem('testProgress');
            }
        }
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = document.getElementById('loadingText');
        text.textContent = message;
        overlay.classList.add('active');
    }

    updateLoadingMessage(message) {
        document.getElementById('loadingText').textContent = message;
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.testingApp = new TestingApp();
    console.log('Testing application initialized');
});
