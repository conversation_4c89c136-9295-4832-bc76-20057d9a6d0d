<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetris Game</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>TETRIS</h1>
            <div class="game-info">
                <div class="score-section">
                    <div class="info-item">
                        <label>Score:</label>
                        <span id="score">0</span>
                    </div>
                    <div class="info-item">
                        <label>Level:</label>
                        <span id="level">1</span>
                    </div>
                    <div class="info-item">
                        <label>Lines:</label>
                        <span id="lines">0</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="game-area">
            <div class="game-board">
                <canvas id="gameCanvas" width="300" height="600"></canvas>
                <div id="gameOverScreen" class="game-over hidden">
                    <h2>Game Over!</h2>
                    <p>Final Score: <span id="finalScore">0</span></p>
                    <button id="restartBtn">Play Again</button>
                </div>
            </div>
            
            <div class="side-panel">
                <div class="next-piece">
                    <h3>Next</h3>
                    <canvas id="nextCanvas" width="120" height="120"></canvas>
                </div>
                
                <div class="controls">
                    <h3>Controls</h3>
                    <div class="control-item">
                        <span>←→</span> Move
                    </div>
                    <div class="control-item">
                        <span>↓</span> Soft Drop
                    </div>
                    <div class="control-item">
                        <span>↑</span> Rotate
                    </div>
                    <div class="control-item">
                        <span>Space</span> Hard Drop
                    </div>
                    <div class="control-item">
                        <span>P</span> Pause
                    </div>
                </div>
                
                <div class="game-buttons">
                    <button id="startBtn">Start Game</button>
                    <button id="pauseBtn" disabled>Pause</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="tetris.js"></script>
</body>
</html>
