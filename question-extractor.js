// Question Extractor Module
class QuestionExtractor {
    constructor() {
        this.questionPatterns = [
            // Numbered questions: "1. What is...?"
            /^(\d+)\.?\s+(.+?\?)\s*$/gm,
            // Parenthetical numbers: "(1) What is...?"
            /^\((\d+)\)\s+(.+?\?)\s*$/gm,
            // Questions without numbers but with question marks
            /^(.+?\?)\s*$/gm
        ];

        this.choicePatterns = [
            // a) choice, b) choice, etc.
            /^([a-z])\)\s+(.+)$/gm,
            // a. choice, b. choice, etc.
            /^([a-z])\.?\s+(.+)$/gm,
            // (a) choice, (b) choice, etc.
            /^\(([a-z])\)\s+(.+)$/gm,
            // A) choice, B) choice, etc.
            /^([A-Z])\)\s+(.+)$/gm,
            // A. choice, B. choice, etc.
            /^([A-Z])\.?\s+(.+)$/gm
        ];

        this.answerKeyPatterns = [
            // Answer: A or Answer: a
            /(?:answer|correct|solution):\s*([a-zA-Z])/gi,
            // 1. A, 2. B, etc.
            /^(\d+)\.?\s*([a-zA-Z])\s*$/gm
        ];
    }

    extractQuestions(text) {
        try {
            const cleanedText = this.preprocessText(text);
            const sections = this.splitIntoSections(cleanedText);
            const questions = [];

            for (const section of sections) {
                const extractedQuestions = this.parseSection(section);
                questions.push(...extractedQuestions);
            }

            // Limit to 50 questions as specified
            const limitedQuestions = questions.slice(0, 50);
            
            return {
                questions: limitedQuestions,
                totalFound: questions.length,
                success: limitedQuestions.length > 0
            };
        } catch (error) {
            console.error('Error extracting questions:', error);
            return {
                questions: [],
                totalFound: 0,
                success: false,
                error: error.message
            };
        }
    }

    preprocessText(text) {
        return text
            // Normalize whitespace
            .replace(/\s+/g, ' ')
            // Fix common OCR errors
            .replace(/['']/g, "'")
            .replace(/[""]/g, '"')
            // Ensure proper spacing around punctuation
            .replace(/([.!?])\s*([A-Z])/g, '$1 $2')
            .trim();
    }

    splitIntoSections(text) {
        // Split by double line breaks or question numbers
        const sections = text.split(/\n\s*\n|\n(?=\d+\.|\d+\)|\(\d+\))/);
        return sections.map(s => s.trim()).filter(s => s.length > 20);
    }

    parseSection(section) {
        const questions = [];
        const lines = section.split('\n').map(line => line.trim());
        
        let currentQuestion = null;
        let currentChoices = [];
        let currentAnswer = null;
        let currentExplanation = null;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (!line) continue;

            // Check if this line is a question
            const questionMatch = this.matchQuestion(line);
            if (questionMatch) {
                // Save previous question if exists
                if (currentQuestion) {
                    questions.push(this.createQuestionObject(
                        currentQuestion, 
                        currentChoices, 
                        currentAnswer, 
                        currentExplanation
                    ));
                }

                // Start new question
                currentQuestion = {
                    number: questionMatch.number,
                    text: questionMatch.text
                };
                currentChoices = [];
                currentAnswer = null;
                currentExplanation = null;
                continue;
            }

            // Check if this line is a choice
            const choiceMatch = this.matchChoice(line);
            if (choiceMatch && currentQuestion) {
                currentChoices.push({
                    letter: choiceMatch.letter.toLowerCase(),
                    text: choiceMatch.text
                });
                continue;
            }

            // Check if this line contains an answer
            const answerMatch = this.matchAnswer(line);
            if (answerMatch && currentQuestion) {
                currentAnswer = answerMatch.toLowerCase();
                continue;
            }

            // Check if this line is an explanation
            if (this.isExplanation(line) && currentQuestion) {
                currentExplanation = line;
                continue;
            }

            // If we have a current question and this doesn't match any pattern,
            // it might be a continuation of the question text
            if (currentQuestion && !choiceMatch && !answerMatch && currentChoices.length === 0) {
                currentQuestion.text += ' ' + line;
            }
        }

        // Don't forget the last question
        if (currentQuestion) {
            questions.push(this.createQuestionObject(
                currentQuestion, 
                currentChoices, 
                currentAnswer, 
                currentExplanation
            ));
        }

        return questions.filter(q => q.choices.length >= 2); // Only return questions with at least 2 choices
    }

    matchQuestion(line) {
        for (const pattern of this.questionPatterns) {
            pattern.lastIndex = 0; // Reset regex
            const match = pattern.exec(line);
            if (match) {
                if (match.length === 3) {
                    // Numbered question
                    return {
                        number: parseInt(match[1]),
                        text: match[2].trim()
                    };
                } else if (match.length === 2) {
                    // Unnumbered question
                    return {
                        number: null,
                        text: match[1].trim()
                    };
                }
            }
        }
        return null;
    }

    matchChoice(line) {
        for (const pattern of this.choicePatterns) {
            pattern.lastIndex = 0; // Reset regex
            const match = pattern.exec(line);
            if (match) {
                return {
                    letter: match[1],
                    text: match[2].trim()
                };
            }
        }
        return null;
    }

    matchAnswer(line) {
        for (const pattern of this.answerKeyPatterns) {
            pattern.lastIndex = 0; // Reset regex
            const match = pattern.exec(line);
            if (match) {
                return match[match.length - 1]; // Return the last capture group (the letter)
            }
        }
        return null;
    }

    isExplanation(line) {
        const explanationKeywords = [
            'explanation', 'rationale', 'because', 'since', 'therefore',
            'the correct answer', 'this is correct', 'this option'
        ];
        
        const lowerLine = line.toLowerCase();
        return explanationKeywords.some(keyword => lowerLine.includes(keyword));
    }

    createQuestionObject(question, choices, answer, explanation) {
        // Auto-number if not numbered
        const questionNumber = question.number || 1;
        
        // Ensure we have at least some choices
        if (choices.length === 0) {
            // Try to extract choices from question text if they're inline
            choices = this.extractInlineChoices(question.text);
        }

        // Generate default choices if still none found
        if (choices.length === 0) {
            choices = [
                { letter: 'a', text: 'Option A' },
                { letter: 'b', text: 'Option B' },
                { letter: 'c', text: 'Option C' },
                { letter: 'd', text: 'Option D' }
            ];
        }

        return {
            id: `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            number: questionNumber,
            text: question.text,
            choices: choices,
            correctAnswer: answer,
            explanation: explanation,
            userAnswer: null,
            flagged: false,
            struckOut: []
        };
    }

    extractInlineChoices(questionText) {
        const choices = [];
        const inlinePattern = /\b([a-d])\)\s*([^a-d\)]+?)(?=\s+[a-d]\)|$)/gi;
        let match;

        while ((match = inlinePattern.exec(questionText)) !== null) {
            choices.push({
                letter: match[1].toLowerCase(),
                text: match[2].trim()
            });
        }

        return choices;
    }

    // Generate sample questions if extraction fails
    generateSampleQuestions() {
        const sampleQuestions = [];
        
        for (let i = 1; i <= 50; i++) {
            sampleQuestions.push({
                id: `sample_${i}`,
                number: i,
                text: `Sample Question ${i}: This is a placeholder question that would be extracted from your document.`,
                choices: [
                    { letter: 'a', text: 'Option A - First choice' },
                    { letter: 'b', text: 'Option B - Second choice' },
                    { letter: 'c', text: 'Option C - Third choice' },
                    { letter: 'd', text: 'Option D - Fourth choice' }
                ],
                correctAnswer: ['a', 'b', 'c', 'd'][Math.floor(Math.random() * 4)],
                explanation: `This is a sample explanation for question ${i}.`,
                userAnswer: null,
                flagged: false,
                struckOut: []
            });
        }

        return sampleQuestions;
    }
}

// Export for use in other modules
window.QuestionExtractor = QuestionExtractor;
