// Document Processor Module
class DocumentProcessor {
    constructor() {
        this.supportedTypes = {
            'application/pdf': 'pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
            'image/jpeg': 'image',
            'image/jpg': 'image',
            'image/png': 'image'
        };
    }

    async processFile(file) {
        const fileType = this.supportedTypes[file.type];
        
        if (!fileType) {
            throw new Error('Unsupported file type. Please upload PDF, Word (.docx), or image files.');
        }

        try {
            switch (fileType) {
                case 'pdf':
                    return await this.processPDF(file);
                case 'docx':
                    return await this.processWord(file);
                case 'image':
                    return await this.processImage(file);
                default:
                    throw new Error('Unknown file type');
            }
        } catch (error) {
            console.error('Error processing file:', error);
            throw new Error(`Failed to process ${fileType.toUpperCase()} file: ${error.message}`);
        }
    }

    async processPDF(file) {
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader();
            
            fileReader.onload = async function() {
                try {
                    const typedArray = new Uint8Array(this.result);
                    
                    // Load PDF document
                    const pdf = await pdfjsLib.getDocument(typedArray).promise;
                    let fullText = '';
                    
                    // Extract text from all pages
                    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                        const page = await pdf.getPage(pageNum);
                        const textContent = await page.getTextContent();
                        
                        const pageText = textContent.items
                            .map(item => item.str)
                            .join(' ');
                        
                        fullText += pageText + '\n\n';
                    }
                    
                    resolve({
                        text: fullText.trim(),
                        pageCount: pdf.numPages,
                        source: 'PDF'
                    });
                } catch (error) {
                    reject(new Error('Failed to parse PDF: ' + error.message));
                }
            };
            
            fileReader.onerror = () => reject(new Error('Failed to read PDF file'));
            fileReader.readAsArrayBuffer(file);
        });
    }

    async processWord(file) {
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader();
            
            fileReader.onload = async function() {
                try {
                    const arrayBuffer = this.result;
                    const result = await mammoth.extractRawText({ arrayBuffer });
                    
                    if (result.messages && result.messages.length > 0) {
                        console.warn('Word processing warnings:', result.messages);
                    }
                    
                    resolve({
                        text: result.value.trim(),
                        source: 'Word Document'
                    });
                } catch (error) {
                    reject(new Error('Failed to parse Word document: ' + error.message));
                }
            };
            
            fileReader.onerror = () => reject(new Error('Failed to read Word file'));
            fileReader.readAsArrayBuffer(file);
        });
    }

    async processImage(file) {
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader();
            
            fileReader.onload = async function() {
                try {
                    const imageData = this.result;
                    
                    // Use Tesseract.js for OCR
                    const { data: { text } } = await Tesseract.recognize(
                        imageData,
                        'eng',
                        {
                            logger: m => {
                                if (m.status === 'recognizing text') {
                                    // Update progress if needed
                                    console.log(`OCR Progress: ${Math.round(m.progress * 100)}%`);
                                }
                            }
                        }
                    );
                    
                    resolve({
                        text: text.trim(),
                        source: 'Image (OCR)'
                    });
                } catch (error) {
                    reject(new Error('Failed to extract text from image: ' + error.message));
                }
            };
            
            fileReader.onerror = () => reject(new Error('Failed to read image file'));
            fileReader.readAsDataURL(file);
        });
    }

    validateFile(file) {
        // Check file type
        if (!this.supportedTypes[file.type]) {
            return {
                valid: false,
                error: 'Unsupported file type. Please upload PDF, Word (.docx), JPEG, or PNG files.'
            };
        }

        // Check file size (50MB limit)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (file.size > maxSize) {
            return {
                valid: false,
                error: 'File size too large. Please upload files smaller than 50MB.'
            };
        }

        return { valid: true };
    }

    getFileInfo(file) {
        return {
            name: file.name,
            size: this.formatFileSize(file.size),
            type: this.supportedTypes[file.type] || 'unknown',
            lastModified: new Date(file.lastModified).toLocaleDateString()
        };
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Clean and normalize extracted text
    cleanText(text) {
        return text
            // Remove excessive whitespace
            .replace(/\s+/g, ' ')
            // Remove special characters that might interfere with parsing
            .replace(/[^\w\s\.\?\!\-\(\)\[\]\{\}:;,'"]/g, '')
            // Normalize line breaks
            .replace(/\n\s*\n/g, '\n\n')
            .trim();
    }

    // Split text into logical sections
    splitIntoSections(text) {
        // Split by double line breaks or common section markers
        const sections = text.split(/\n\s*\n|\n\s*(?=\d+\.|\d+\)|\([a-z]\)|\[a-z\])/);
        
        return sections
            .map(section => section.trim())
            .filter(section => section.length > 10); // Filter out very short sections
    }

    // Detect if text contains questions
    hasQuestions(text) {
        const questionPatterns = [
            /\?\s*$/m,                          // Ends with question mark
            /^(?:\d+\.|\d+\))\s*.+\?/m,        // Numbered questions
            /what|which|who|when|where|why|how/i, // Question words
            /choose|select|identify|determine/i,   // Instruction words
            /\b[a-d]\)|[a-d]\.|\([a-d]\)/i     // Multiple choice indicators
        ];

        return questionPatterns.some(pattern => pattern.test(text));
    }
}

// Export for use in other modules
window.DocumentProcessor = DocumentProcessor;
