<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="paperGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="timerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="60" fill="url(#backgroundGradient)" filter="url(#shadow)"/>
  
  <!-- Document/Paper -->
  <rect x="25" y="20" width="50" height="65" rx="3" ry="3" fill="url(#paperGradient)" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- Document Lines (representing questions) -->
  <line x1="30" y1="30" x2="65" y2="30" stroke="#3498db" stroke-width="2" stroke-linecap="round"/>
  <line x1="30" y1="37" x2="60" y2="37" stroke="#95a5a6" stroke-width="1.5" stroke-linecap="round"/>
  <line x1="30" y1="44" x2="62" y2="44" stroke="#95a5a6" stroke-width="1.5" stroke-linecap="round"/>
  
  <!-- Multiple Choice Options (A, B, C, D) -->
  <circle cx="32" cy="52" r="3" fill="none" stroke="#3498db" stroke-width="1.5"/>
  <text x="38" y="55" font-family="Arial, sans-serif" font-size="8" fill="#2c3e50">A</text>
  
  <circle cx="32" cy="60" r="3" fill="#3498db" stroke="#3498db" stroke-width="1.5"/>
  <text x="38" y="63" font-family="Arial, sans-serif" font-size="8" fill="#2c3e50">B</text>
  
  <circle cx="32" cy="68" r="3" fill="none" stroke="#3498db" stroke-width="1.5"/>
  <text x="38" y="71" font-family="Arial, sans-serif" font-size="8" fill="#2c3e50">C</text>
  
  <circle cx="32" cy="76" r="3" fill="none" stroke="#3498db" stroke-width="1.5"/>
  <text x="38" y="79" font-family="Arial, sans-serif" font-size="8" fill="#2c3e50">D</text>
  
  <!-- Timer Icon -->
  <circle cx="85" cy="35" r="12" fill="url(#timerGradient)" stroke="#ffffff" stroke-width="2"/>
  <circle cx="85" cy="35" r="8" fill="none" stroke="#ffffff" stroke-width="1.5"/>
  <line x1="85" y1="35" x2="85" y2="30" stroke="#ffffff" stroke-width="2" stroke-linecap="round"/>
  <line x1="85" y1="35" x2="89" y2="35" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round"/>
  
  <!-- Progress Indicator -->
  <rect x="25" y="95" width="50" height="8" rx="4" ry="4" fill="#ecf0f1"/>
  <rect x="25" y="95" width="20" height="8" rx="4" ry="4" fill="#3498db"/>
  
  <!-- Question Number -->
  <text x="85" y="100" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#ffffff" text-anchor="middle">Q</text>
  <text x="85" y="112" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">1/50</text>
  
  <!-- Upload Arrow (subtle) -->
  <path d="M 45 15 L 50 10 L 55 15 M 50 10 L 50 20" stroke="#3498db" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" opacity="0.7"/>
</svg>
