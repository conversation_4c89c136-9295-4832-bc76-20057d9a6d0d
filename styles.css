* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.game-container {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    max-width: 800px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    font-size: 3rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 15px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.game-info {
    display: flex;
    justify-content: center;
}

.score-section {
    display: flex;
    gap: 30px;
}

.info-item {
    text-align: center;
}

.info-item label {
    display: block;
    font-size: 0.9rem;
    color: #ccc;
    margin-bottom: 5px;
}

.info-item span {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4ecdc4;
}

.game-area {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: flex-start;
}

.game-board {
    position: relative;
    border: 3px solid #4ecdc4;
    border-radius: 10px;
    background: #000;
    box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
}

#gameCanvas {
    display: block;
    border-radius: 7px;
}

.game-over {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    border: 2px solid #ff6b6b;
}

.game-over h2 {
    color: #ff6b6b;
    margin-bottom: 15px;
    font-size: 2rem;
}

.game-over p {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.hidden {
    display: none;
}

.side-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 200px;
}

.next-piece {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.next-piece h3 {
    margin-bottom: 10px;
    color: #4ecdc4;
}

#nextCanvas {
    border: 2px solid #4ecdc4;
    border-radius: 5px;
    background: #000;
}

.controls {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
}

.controls h3 {
    margin-bottom: 15px;
    color: #4ecdc4;
    text-align: center;
}

.control-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.control-item span:first-child {
    font-weight: bold;
    color: #96ceb4;
}

.game-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

button {
    padding: 12px 20px;
    font-size: 1rem;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

#startBtn {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

#startBtn:hover {
    background: linear-gradient(45deg, #44a08d, #4ecdc4);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
}

#pauseBtn {
    background: linear-gradient(45deg, #ffa726, #ff7043);
    color: white;
}

#pauseBtn:hover:not(:disabled) {
    background: linear-gradient(45deg, #ff7043, #ffa726);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 167, 38, 0.4);
}

#restartBtn {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

#restartBtn:hover {
    background: linear-gradient(45deg, #44a08d, #4ecdc4);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
}

button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

@media (max-width: 768px) {
    .game-area {
        flex-direction: column;
        align-items: center;
    }
    
    .side-panel {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        min-width: auto;
    }
    
    .game-header h1 {
        font-size: 2rem;
    }
    
    .score-section {
        gap: 15px;
    }
}
